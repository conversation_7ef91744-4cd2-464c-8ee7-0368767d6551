package config

import (
	"fmt"
	"log"
	"os"
	"strconv"

	"gopkg.in/yaml.v2"
)

// Config holds all configuration for the application
type Config struct {
	Server      ServerConfig      `yaml:"server"`
	Supabase    SupabaseConfig    `yaml:"supabase"`
	Logto       LogtoConfig       `yaml:"logto"`
	Casdoor     CasdoorConfig     `yaml:"casdoor"`
	Database    DatabaseConfig    `yaml:"database"`
	LL<PERSON>rovider LLMProviderConfig `yaml:"llmProvider"`
	Logging     LoggingConfig     `yaml:"logging"`
	Security    SecurityConfig    `yaml:"security"`
	Performance PerformanceConfig `yaml:"performance"`
	Development DevelopmentConfig `yaml:"development"`
	Testing     TestingConfig     `yaml:"testing"`
	Monitoring  MonitoringConfig  `yaml:"monitoring"`
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Port int `yaml:"port"`
}

// SupabaseConfig holds Supabase-related configuration
type SupabaseConfig struct {
	URL string `yaml:"url"`
	Key string `yaml:"key"`
}

// LogtoConfig holds Logto-related configuration
type LogtoConfig struct {
	Endpoint           string `yaml:"endpoint"`
	AppID              string `yaml:"appId"`
	AppSecret          string `yaml:"appSecret"`
	RedirectURI        string `yaml:"redirectUri"`
	PostLogoutRedirectURI string `yaml:"postLogoutRedirectUri"`
}

// CasdoorConfig holds Casdoor-related configuration
type CasdoorConfig struct {
	Endpoint           string `yaml:"endpoint"`
	ClientID           string `yaml:"clientId"`
	ClientSecret       string `yaml:"clientSecret"`
	JwtPublicKey       string `yaml:"jwtPublicKey"`
	OrganizationName   string `yaml:"organizationName"`
	ApplicationName    string `yaml:"applicationName"`
	RedirectURI        string `yaml:"redirectUri"`
	PostLogoutRedirectURI string `yaml:"postLogoutRedirectUri"`
}

// DatabaseConfig holds database-related configuration
type DatabaseConfig struct {
	Type    string `yaml:"type"` // "sqlite" or "postgres"
	DSN     string `yaml:"dsn"`  // Data Source Name
	UseGORM bool   `yaml:"useGorm"`
}

// LLMProviderConfig holds LLM provider configuration
type LLMProviderConfig struct {
	SystemProviders []SystemLLMProvider `yaml:"systemProviders"`
	InitialCredits  int                 `yaml:"initialCredits"`
}

// SystemLLMProvider represents a built-in system LLM provider
type SystemLLMProvider struct {
	ID               string  `yaml:"id"`
	Name             string  `yaml:"name"`
	Alias            string  `yaml:"alias"`
	Type             string  `yaml:"type"`
	APIKey           string  `yaml:"apiKey"`
	BaseURL          string  `yaml:"baseUrl"`
	Model            string  `yaml:"model"`
	SystemPrompt     string  `yaml:"systemPrompt"`
	IsDefault        bool    `yaml:"isDefault"`
	Temperature      float64 `yaml:"temperature"`
	MaxTokens        int     `yaml:"maxTokens"`
	TopP             float64 `yaml:"topP"`
	FrequencyPenalty float64 `yaml:"frequencyPenalty"`
	PresencePenalty  float64 `yaml:"presencePenalty"`
	Enabled          bool    `yaml:"enabled"`
}

// LoggingConfig holds logging-related configuration
type LoggingConfig struct {
	Level  string `yaml:"level"`  // debug, info, warn, error
	Format string `yaml:"format"` // text, json
	Output string `yaml:"output"` // console, file
	File   string `yaml:"file"`   // log file path
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	SecureCookies bool     `yaml:"secureCookies"`
	CorsOrigins   []string `yaml:"corsOrigins"`
	RateLimit     struct {
		Enabled           bool `yaml:"enabled"`
		RequestsPerMinute int  `yaml:"requestsPerMinute"`
		BurstSize         int  `yaml:"burstSize"`
	} `yaml:"rateLimit"`
}

// PerformanceConfig holds performance-related configuration
type PerformanceConfig struct {
	GzipEnabled  bool `yaml:"gzipEnabled"`
	CacheEnabled bool `yaml:"cacheEnabled"`
	CacheTTL     int  `yaml:"cacheTTL"`
}

// DevelopmentConfig holds development-specific configuration
type DevelopmentConfig struct {
	HotReload      bool `yaml:"hotReload"`
	DebugEndpoints bool `yaml:"debugEndpoints"`
	RequestLogging bool `yaml:"requestLogging"`
	SQLLogging     bool `yaml:"sqlLogging"`
}

// TestingConfig holds testing-specific configuration
type TestingConfig struct {
	TestEndpoints bool `yaml:"testEndpoints"`
	MockServices  bool `yaml:"mockServices"`
	FastMode      bool `yaml:"fastMode"`
}

// MonitoringConfig holds monitoring-related configuration
type MonitoringConfig struct {
	HealthCheckEnabled bool   `yaml:"healthCheckEnabled"`
	MetricsEnabled     bool   `yaml:"metricsEnabled"`
	MetricsPath        string `yaml:"metricsPath"`
}

var cfg *Config

// Load loads configuration from environment variables and config files
func Load() (*Config, error) {
	log.Println("Loading configuration...")

	// Determine environment
	env := getEnv("APP_ENV", "development")
	log.Printf("Running in %s environment", env)

	// 优先从环境变量加载基础配置
	cfg = &Config{
		Server: ServerConfig{
			Port: getEnvAsInt("PORT", 8080),
		},
		Supabase: SupabaseConfig{
			URL: getEnv("SUPABASE_URL", ""),
			Key: getEnv("SUPABASE_KEY", ""),
		},
		Logto: LogtoConfig{
			Endpoint:              getEnv("LOGTO_ENDPOINT", ""),
			AppID:                 getEnv("LOGTO_APP_ID", ""),
			AppSecret:             getEnv("LOGTO_APP_SECRET", ""),
			RedirectURI:           getEnv("LOGTO_REDIRECT_URI", ""),
			PostLogoutRedirectURI: getEnv("LOGTO_POST_LOGOUT_REDIRECT_URI", ""),
		},
		Casdoor: CasdoorConfig{
			Endpoint:              getEnv("CASDOOR_ENDPOINT", ""),
			ClientID:              getEnv("CASDOOR_CLIENT_ID", ""),
			ClientSecret:          getEnv("CASDOOR_CLIENT_SECRET", ""),
			JwtPublicKey:          getEnv("CASDOOR_JWT_PUBLIC_KEY", ""),
			OrganizationName:      getEnv("CASDOOR_ORGANIZATION_NAME", ""),
			ApplicationName:       getEnv("CASDOOR_APPLICATION_NAME", ""),
			RedirectURI:           getEnv("CASDOOR_REDIRECT_URI", ""),
			PostLogoutRedirectURI: getEnv("CASDOOR_POST_LOGOUT_REDIRECT_URI", ""),
		},
		Database: DatabaseConfig{
			Type:    getEnv("DB_TYPE", "sqlite"),
			DSN:     getEnv("DB_DSN", "file:./data.db?cache=shared"),
			UseGORM: getEnvAsBool("USE_GORM", true),
		},
		LLMProvider: LLMProviderConfig{
			SystemProviders: []SystemLLMProvider{}, // Will be loaded from YAML
			InitialCredits:  10000,                  // Default initial credits for new users
		},
		// Default configurations
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "text"),
			Output: getEnv("LOG_OUTPUT", "console"),
			File:   getEnv("LOG_FILE", ""),
		},
		Security: SecurityConfig{
			SecureCookies: getEnvAsBool("SECURE_COOKIES", false),
		},
		Performance: PerformanceConfig{
			GzipEnabled:  getEnvAsBool("GZIP_ENABLED", true),
			CacheEnabled: getEnvAsBool("CACHE_ENABLED", true),
			CacheTTL:     getEnvAsInt("CACHE_TTL", 3600),
		},
		Development: DevelopmentConfig{
			HotReload:      getEnvAsBool("HOT_RELOAD", false),
			DebugEndpoints: getEnvAsBool("DEBUG_ENDPOINTS", false),
			RequestLogging: getEnvAsBool("REQUEST_LOGGING", false),
			SQLLogging:     getEnvAsBool("SQL_LOGGING", false),
		},
		Testing: TestingConfig{
			TestEndpoints: getEnvAsBool("TEST_ENDPOINTS", false),
			MockServices:  getEnvAsBool("MOCK_SERVICES", false),
			FastMode:      getEnvAsBool("FAST_MODE", false),
		},
		Monitoring: MonitoringConfig{
			HealthCheckEnabled: getEnvAsBool("HEALTH_CHECK_ENABLED", true),
			MetricsEnabled:     getEnvAsBool("METRICS_ENABLED", false),
			MetricsPath:        getEnv("METRICS_PATH", "/metrics"),
		},
	}

	// 根据环境加载对应的配置文件
	// 支持多个可能的配置文件路径
	configFiles := []string{
		fmt.Sprintf("config.%s.yaml", env),
		"config.yaml", // fallback
		fmt.Sprintf("backend/config.%s.yaml", env), // 从项目根目录运行时
		"backend/config.yaml", // 从项目根目录运行时的fallback
	}

	var yamlConfig Config
	configLoaded := false

	for _, configFile := range configFiles {
		log.Printf("Attempting to load %s...", configFile)
		data, err := os.ReadFile(configFile)
		if err != nil {
			log.Printf("Error reading %s: %v", configFile, err)
			continue
		}

		if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
			log.Printf("Error parsing %s: %v", configFile, err)
			continue
		}

		log.Printf("Successfully loaded configuration from %s", configFile)
		configLoaded = true
		break
	}

	if configLoaded {
		// 合并配置
		mergeYAMLConfig(cfg, &yamlConfig)
	}

	// 打印最终配置
	log.Println("Final configuration loaded successfully")
	log.Printf("Environment: %s", env)
	log.Printf("Server Port: %d", cfg.Server.Port)
	log.Printf("Database: Type=%s, UseGORM=%v", cfg.Database.Type, cfg.Database.UseGORM)
	log.Printf("Logging: Level=%s, Format=%s", cfg.Logging.Level, cfg.Logging.Format)

	return cfg, nil
}

// Get returns the current configuration
func Get() *Config {
	return cfg
}

// Helper function to get environment variable as string
func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// Helper function to get environment variable as int
func getEnvAsInt(key string, defaultValue int) int {
	if value, exists := os.LookupEnv(key); exists {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// Helper function to get environment variable as bool
func getEnvAsBool(key string, defaultValue bool) bool {
	if value, exists := os.LookupEnv(key); exists {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// mergeYAMLConfig merges YAML configuration into the main config
func mergeYAMLConfig(cfg *Config, yamlConfig *Config) {
	// Server configuration
	if cfg.Server.Port == 8080 && yamlConfig.Server.Port != 0 {
		cfg.Server.Port = yamlConfig.Server.Port
	}

	// Supabase configuration
	if cfg.Supabase.URL == "" && yamlConfig.Supabase.URL != "" {
		cfg.Supabase.URL = yamlConfig.Supabase.URL
	}
	if cfg.Supabase.Key == "" && yamlConfig.Supabase.Key != "" {
		cfg.Supabase.Key = yamlConfig.Supabase.Key
	}

	// Logto configuration
	if cfg.Logto.Endpoint == "" && yamlConfig.Logto.Endpoint != "" {
		cfg.Logto.Endpoint = yamlConfig.Logto.Endpoint
	}
	if cfg.Logto.AppID == "" && yamlConfig.Logto.AppID != "" {
		cfg.Logto.AppID = yamlConfig.Logto.AppID
	}
	if cfg.Logto.AppSecret == "" && yamlConfig.Logto.AppSecret != "" {
		cfg.Logto.AppSecret = yamlConfig.Logto.AppSecret
	}
	if cfg.Logto.RedirectURI == "" && yamlConfig.Logto.RedirectURI != "" {
		cfg.Logto.RedirectURI = yamlConfig.Logto.RedirectURI
	}
	if cfg.Logto.PostLogoutRedirectURI == "" && yamlConfig.Logto.PostLogoutRedirectURI != "" {
		cfg.Logto.PostLogoutRedirectURI = yamlConfig.Logto.PostLogoutRedirectURI
	}

	// Casdoor configuration
	if cfg.Casdoor.Endpoint == "" && yamlConfig.Casdoor.Endpoint != "" {
		cfg.Casdoor.Endpoint = yamlConfig.Casdoor.Endpoint
	}
	if cfg.Casdoor.ClientID == "" && yamlConfig.Casdoor.ClientID != "" {
		cfg.Casdoor.ClientID = yamlConfig.Casdoor.ClientID
	}
	if cfg.Casdoor.ClientSecret == "" && yamlConfig.Casdoor.ClientSecret != "" {
		cfg.Casdoor.ClientSecret = yamlConfig.Casdoor.ClientSecret
	}
	if cfg.Casdoor.JwtPublicKey == "" && yamlConfig.Casdoor.JwtPublicKey != "" {
		cfg.Casdoor.JwtPublicKey = yamlConfig.Casdoor.JwtPublicKey
	}
	if cfg.Casdoor.OrganizationName == "" && yamlConfig.Casdoor.OrganizationName != "" {
		cfg.Casdoor.OrganizationName = yamlConfig.Casdoor.OrganizationName
	}
	if cfg.Casdoor.ApplicationName == "" && yamlConfig.Casdoor.ApplicationName != "" {
		cfg.Casdoor.ApplicationName = yamlConfig.Casdoor.ApplicationName
	}
	if cfg.Casdoor.RedirectURI == "" && yamlConfig.Casdoor.RedirectURI != "" {
		cfg.Casdoor.RedirectURI = yamlConfig.Casdoor.RedirectURI
	}
	if cfg.Casdoor.PostLogoutRedirectURI == "" && yamlConfig.Casdoor.PostLogoutRedirectURI != "" {
		cfg.Casdoor.PostLogoutRedirectURI = yamlConfig.Casdoor.PostLogoutRedirectURI
	}

	// Database configuration
	if os.Getenv("DB_TYPE") == "" && yamlConfig.Database.Type != "" {
		cfg.Database.Type = yamlConfig.Database.Type
	}
	if os.Getenv("DB_DSN") == "" && yamlConfig.Database.DSN != "" {
		cfg.Database.DSN = yamlConfig.Database.DSN
	}
	if os.Getenv("USE_GORM") == "" {
		cfg.Database.UseGORM = yamlConfig.Database.UseGORM
	}

	// LLM Provider configuration
	if len(yamlConfig.LLMProvider.SystemProviders) > 0 {
		cfg.LLMProvider.SystemProviders = yamlConfig.LLMProvider.SystemProviders
	}
	if yamlConfig.LLMProvider.InitialCredits > 0 {
		cfg.LLMProvider.InitialCredits = yamlConfig.LLMProvider.InitialCredits
	}

	// Merge other configurations
	mergeConfigFromYAML(cfg, yamlConfig)
}

// mergeConfigFromYAML merges YAML configuration into the main config
func mergeConfigFromYAML(cfg *Config, yamlConfig *Config) {
	// Logging configuration
	if os.Getenv("LOG_LEVEL") == "" && yamlConfig.Logging.Level != "" {
		cfg.Logging.Level = yamlConfig.Logging.Level
	}
	if os.Getenv("LOG_FORMAT") == "" && yamlConfig.Logging.Format != "" {
		cfg.Logging.Format = yamlConfig.Logging.Format
	}
	if os.Getenv("LOG_OUTPUT") == "" && yamlConfig.Logging.Output != "" {
		cfg.Logging.Output = yamlConfig.Logging.Output
	}
	if os.Getenv("LOG_FILE") == "" && yamlConfig.Logging.File != "" {
		cfg.Logging.File = yamlConfig.Logging.File
	}

	// Security configuration
	if os.Getenv("SECURE_COOKIES") == "" {
		cfg.Security.SecureCookies = yamlConfig.Security.SecureCookies
	}
	if len(yamlConfig.Security.CorsOrigins) > 0 {
		cfg.Security.CorsOrigins = yamlConfig.Security.CorsOrigins
	}
	if yamlConfig.Security.RateLimit.Enabled || yamlConfig.Security.RateLimit.RequestsPerMinute > 0 {
		cfg.Security.RateLimit = yamlConfig.Security.RateLimit
	}

	// Performance configuration
	if os.Getenv("GZIP_ENABLED") == "" {
		cfg.Performance.GzipEnabled = yamlConfig.Performance.GzipEnabled
	}
	if os.Getenv("CACHE_ENABLED") == "" {
		cfg.Performance.CacheEnabled = yamlConfig.Performance.CacheEnabled
	}
	if os.Getenv("CACHE_TTL") == "" && yamlConfig.Performance.CacheTTL > 0 {
		cfg.Performance.CacheTTL = yamlConfig.Performance.CacheTTL
	}

	// Development configuration
	if os.Getenv("HOT_RELOAD") == "" {
		cfg.Development.HotReload = yamlConfig.Development.HotReload
	}
	if os.Getenv("DEBUG_ENDPOINTS") == "" {
		cfg.Development.DebugEndpoints = yamlConfig.Development.DebugEndpoints
	}
	if os.Getenv("REQUEST_LOGGING") == "" {
		cfg.Development.RequestLogging = yamlConfig.Development.RequestLogging
	}
	if os.Getenv("SQL_LOGGING") == "" {
		cfg.Development.SQLLogging = yamlConfig.Development.SQLLogging
	}

	// Testing configuration
	if os.Getenv("TEST_ENDPOINTS") == "" {
		cfg.Testing.TestEndpoints = yamlConfig.Testing.TestEndpoints
	}
	if os.Getenv("MOCK_SERVICES") == "" {
		cfg.Testing.MockServices = yamlConfig.Testing.MockServices
	}
	if os.Getenv("FAST_MODE") == "" {
		cfg.Testing.FastMode = yamlConfig.Testing.FastMode
	}

	// Monitoring configuration
	if os.Getenv("HEALTH_CHECK_ENABLED") == "" {
		cfg.Monitoring.HealthCheckEnabled = yamlConfig.Monitoring.HealthCheckEnabled
	}
	if os.Getenv("METRICS_ENABLED") == "" {
		cfg.Monitoring.MetricsEnabled = yamlConfig.Monitoring.MetricsEnabled
	}
	if os.Getenv("METRICS_PATH") == "" && yamlConfig.Monitoring.MetricsPath != "" {
		cfg.Monitoring.MetricsPath = yamlConfig.Monitoring.MetricsPath
	}
}
